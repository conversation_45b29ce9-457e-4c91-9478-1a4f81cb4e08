package handlers

import (
	"database/sql"
	"net/http"

	"github.com/bridge/bridge/internal/services"
	"github.com/bridge/bridge/pkg/auth"
	"github.com/bridge/bridge/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type AuthHandler struct {
	authService *auth.Service
}

func NewAuthHandler(authService *auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Tenant context not found",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	// Get user by email
	user, err := userService.GetUserByEmail(req.Email)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Validate password
	if err := userService.ValidatePassword(user, req.Password); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate JWT token
	token, expiresAt, err := h.authService.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate token",
		})
		return
	}

	// Return login response
	response := models.LoginResponse{
		Token:     token,
		User:      *user,
		ExpiresAt: expiresAt,
	}

	c.JSON(http.StatusOK, response)
}

func (h *AuthHandler) RefreshToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Authorization header is required",
		})
		return
	}

	tokenString, err := h.authService.ExtractTokenFromHeader(authHeader)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid authorization header format",
		})
		return
	}

	newToken, expiresAt, err := h.authService.RefreshToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Unable to refresh token",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":      newToken,
		"expires_at": expiresAt,
	})
}

func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User ID not found in context",
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant context not found",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	user, err := userService.GetUserByID(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "User not found",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User ID not found in context",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Users can only update their own first_name and last_name
	// Role and IsActive changes require admin privileges
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		req.Role = nil
		req.IsActive = nil
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant context not found",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	user, err := userService.UpdateUser(userID.(uuid.UUID), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to update profile",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, user)
}
