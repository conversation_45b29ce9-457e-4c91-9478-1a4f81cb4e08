package handlers

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/bridge/bridge/internal/services"
	"github.com/bridge/bridge/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type UserHandler struct{}

func NewUserHandler() *UserHandler {
	return &UserHandler{}
}

func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID not found in context",
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Tenant database not found in context",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	user, err := userService.CreateUser(tenantID.(uuid.UUID), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to create user",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, user)
}

func (h *UserHandler) GetUser(c *gin.Context) {
	userIDParam := c.Param("id")
	userID, err := uuid.Parse(userIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID format",
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant database not found in context",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	user, err := userService.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "User not found",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

func (h *UserHandler) UpdateUser(c *gin.Context) {
	userIDParam := c.Param("id")
	userID, err := uuid.Parse(userIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID format",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant database not found in context",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	user, err := userService.UpdateUser(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to update user",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID not found in context",
		})
		return
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant database not found in context",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	users, err := userService.ListUsers(tenantID.(uuid.UUID), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list users",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"users":  users,
		"limit":  limit,
		"offset": offset,
		"count":  len(users),
	})
}

func (h *UserHandler) DeleteUser(c *gin.Context) {
	userIDParam := c.Param("id")
	userID, err := uuid.Parse(userIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID format",
		})
		return
	}

	// Soft delete by setting is_active to false
	req := models.UpdateUserRequest{
		IsActive: &[]bool{false}[0],
	}

	// Get tenant database from context
	tenantDB, exists := c.Get("tenant_db")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant database not found in context",
		})
		return
	}

	db := tenantDB.(*sql.DB)
	userService := services.NewUserService(db)

	_, err = userService.UpdateUser(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to delete user",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User deleted successfully",
	})
}
