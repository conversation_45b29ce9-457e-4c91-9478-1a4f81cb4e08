# Bridge API Architecture

## Overview

Bridge is a multi-tenant project management API built with Go, following idiomatic Go project structure patterns inspired by Grafana and other major Go projects.

## Project Structure

```
bridge/
├── cmd/                    # Application entry points
│   └── bridge/            # Main application
│       └── main.go        # Application entry point
├── pkg/                   # Public library code (importable by external apps)
│   ├── api/              # API layer
│   │   ├── handlers/     # HTTP request handlers
│   │   └── routes/       # Route definitions
│   ├── auth/             # Authentication utilities
│   │   ├── jwt.go        # JWT service
│   │   └── jwt_test.go   # JWT tests
│   ├── config/           # Configuration management
│   │   └── config.go     # Configuration loading
│   ├── database/         # Database utilities
│   │   └── connection.go # Multi-tenant DB manager
│   └── models/           # Data models
│       ├── user.go       # User models
│       ├── tenant.go     # Tenant models
│       └── project.go    # Project models
├── internal/             # Private application code (not importable)
│   ├── server/           # HTTP server setup
│   │   └── server.go     # Server implementation
│   ├── services/         # Business logic services
│   │   └── user_service.go # User business logic
│   └── middleware/       # HTTP middleware
│       ├── auth.go       # Authentication middleware
│       ├── tenant.go     # Tenant middleware
│       └── cors.go       # CORS middleware
├── migrations/           # Database migrations
│   ├── 001_create_master_schema.sql
│   └── 002_create_tenant_schema.sql
├── scripts/              # Build and setup scripts
│   ├── setup.sql         # Database setup
│   └── sample_data.sql   # Sample data
├── build/                # Build artifacts (generated)
├── Makefile             # Build automation
├── go.mod               # Go module definition
├── go.sum               # Go module checksums
├── .env.example         # Environment variables template
└── README.md            # Project documentation
```

## Design Principles

### 1. Standard Go Project Layout

The project follows the [Standard Go Project Layout](https://github.com/golang-standards/project-layout):

- **`cmd/`**: Main applications for this project
- **`pkg/`**: Library code that's ok to use by external applications
- **`internal/`**: Private application and library code

### 2. Multi-Tenant Architecture

- **Physical Separation**: Each tenant has its own database for complete data isolation
- **Master Database**: Stores tenant metadata and configuration
- **Dynamic Connections**: Automatic tenant database connection management

### 3. Clean Architecture

- **Separation of Concerns**: Clear boundaries between layers
- **Dependency Injection**: Services are injected into handlers
- **Interface-Based Design**: Easy to test and mock

## Key Components

### Authentication (`pkg/auth/`)

- JWT-based authentication with tenant identification
- Token generation, validation, and refresh
- Secure token extraction from headers

### Database Layer (`pkg/database/`)

- Multi-tenant database connection manager
- Master database for tenant management
- Tenant database connection pooling
- Automatic migration support

### API Layer (`pkg/api/`)

- RESTful HTTP handlers
- Route definitions with middleware
- Request/response models
- Input validation

### Business Logic (`internal/services/`)

- User management service
- Password hashing and validation
- CRUD operations with proper error handling

### Middleware (`internal/middleware/`)

- Authentication middleware
- Tenant identification and context
- CORS handling
- Role-based access control

## Multi-Tenant Strategy

### Tenant Identification

1. **JWT Token**: Tenant ID embedded in authentication token (primary)
2. **X-Tenant-ID Header**: Explicit tenant ID in request header
3. **Subdomain**: Extract tenant from subdomain (e.g., `acme.api.example.com`)

### Database Isolation

```
Master DB (bridge_master)
├── tenants table
└── tenant metadata

Tenant DBs
├── bridge_tenant_acme
├── bridge_tenant_company2
└── bridge_tenant_company3
```

### Connection Management

- Singleton database manager
- Lazy connection establishment
- Connection pooling per tenant
- Automatic cleanup and error handling

## Security Features

- **Password Hashing**: bcrypt with salt
- **JWT Tokens**: Signed with HMAC-SHA256
- **Role-Based Access**: Admin, Manager, Member, Viewer
- **Tenant Isolation**: Physical database separation
- **Input Validation**: Request validation with Gin binding

## API Design

### RESTful Endpoints

- **Authentication**: `/api/v1/auth/*`
- **User Management**: `/api/v1/users/*`
- **Profile**: `/api/v1/profile`
- **Projects**: `/api/v1/projects/*` (planned)
- **Admin**: `/api/v1/admin/*`

### Response Format

```json
{
  "data": {...},
  "error": "error message if any",
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "v1"
  }
}
```

## Development Workflow

### Building

```bash
# Development
make dev

# Production build
make build

# Cross-platform builds
make build-linux
make build-windows
make build-darwin
```

### Testing

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Lint code
make lint
```

### Database Management

```bash
# Setup databases
make setup-db
make create-tenant-db

# Run migrations
make migrate-master
make migrate-tenant
```

## Deployment Considerations

### Environment Variables

- Database connection settings
- JWT secret configuration
- CORS settings
- Logging configuration

### Scaling

- Horizontal scaling with load balancers
- Database connection pooling
- Caching layer (Redis) for sessions
- CDN for static assets

### Monitoring

- Health check endpoints
- Metrics collection
- Error tracking
- Performance monitoring

## Future Enhancements

1. **WebSocket Support**: Real-time updates
2. **File Upload**: Document management
3. **Audit Logging**: Track all changes
4. **Email Notifications**: User notifications
5. **API Rate Limiting**: Prevent abuse
6. **Caching Layer**: Improve performance
7. **Backup Strategy**: Data protection
8. **Monitoring Dashboard**: Operational insights
