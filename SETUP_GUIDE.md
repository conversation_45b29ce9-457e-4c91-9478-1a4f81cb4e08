# Bridge API Setup Guide

This guide will help you set up the Bridge multi-tenant project management API from scratch.

## Prerequisites

1. **Go 1.21+** - [Download Go](https://golang.org/dl/)
2. **PostgreSQL 12+** - [Download PostgreSQL](https://www.postgresql.org/download/)
3. **Git** (optional) - For version control

## Step-by-Step Setup

### 1. Environment Setup

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Edit `.env` with your database credentials:
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_postgres_user
DB_PASSWORD=your_postgres_password
DB_NAME=bridge_master
DB_SSL_MODE=disable

# JWT Configuration (CHANGE THIS IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

### 2. Database Setup

**Option A: Using Make (Recommended)**
```bash
# Set up everything automatically
make setup-db
make create-tenant-db
```

**Option B: Manual Setup**
1. Create the master database:
```bash
createdb bridge_master
```

2. Run the master database migration:
```bash
psql -d bridge_master -f migrations/001_create_master_schema.sql
```

3. Set up a sample tenant:
```bash
psql -d bridge_master -f scripts/setup.sql
```

4. Create the tenant database:
```bash
createdb bridge_tenant_acme
```

5. Run the tenant database migration:
```bash
psql -d bridge_tenant_acme -f migrations/002_create_tenant_schema.sql
```

6. Add sample data to the tenant database:
```bash
psql -d bridge_tenant_acme -f scripts/sample_data.sql
```

### 3. Install Dependencies

```bash
# Using Make
make deps

# Or directly
go mod download
```

### 4. Build and Run

**Option A: Using Make (Recommended)**
```bash
# Development mode (with hot reload)
make dev

# Build and run
make build
make run
```

**Option B: Manual**
```bash
# Build the application
go build -o build/bridge cmd/bridge/main.go

# Run the application
./build/bridge

# Or run directly
go run cmd/bridge/main.go
```

The API will be available at `http://localhost:8080`

### 5. Test the API

1. Check health endpoint:
```bash
curl http://localhost:8080/health
```

2. Login with sample user:
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-446655440000" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

3. Use the returned JWT token for authenticated requests:
```bash
curl http://localhost:8080/api/v1/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-446655440000"
```

## Sample Users

The setup creates these sample users in the `acme` tenant:

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | admin123 | admin |
| <EMAIL> | manager123 | manager |
| <EMAIL> | member123 | member |

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Login
- `POST /api/v1/auth/refresh` - Refresh token

### User Profile
- `GET /api/v1/profile` - Get current user profile
- `PUT /api/v1/profile` - Update current user profile

### User Management (Admin/Manager only)
- `POST /api/v1/users` - Create user
- `GET /api/v1/users` - List users
- `GET /api/v1/users/:id` - Get user
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user

## Tenant Identification

The API supports multiple ways to identify tenants:

1. **X-Tenant-ID Header** (recommended for development):
```bash
curl -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-446655440000" ...
```

2. **JWT Token** (automatic after login)
3. **Subdomain** (for production deployment)

## Development

### Running Tests
```bash
# Using Make
make test

# With coverage
make test-coverage

# Or directly
go test ./...
```

### Adding New Features
1. Define models in `pkg/models/`
2. Create services in `internal/services/`
3. Implement handlers in `pkg/api/handlers/`
4. Add routes in `pkg/api/routes/router.go`

### Database Migrations
- Master DB migrations: `migrations/001_*.sql`
- Tenant DB migrations: `migrations/002_*.sql`

## Troubleshooting

### Common Issues

1. **Database connection failed**
   - Check PostgreSQL is running
   - Verify credentials in `.env`
   - Ensure databases exist

2. **JWT token invalid**
   - Check JWT_SECRET in `.env`
   - Ensure token hasn't expired

3. **Tenant not found**
   - Verify tenant exists in master database
   - Check tenant database is created and migrated

### Logs
The application logs important events. Check console output for debugging information.

## Production Deployment

1. Set `GIN_MODE=release` in environment
2. Use a strong `JWT_SECRET`
3. Configure proper CORS origins
4. Use connection pooling for databases
5. Set up proper logging and monitoring
6. Use HTTPS in production

## Next Steps

1. Implement project management handlers
2. Add board and task management
3. Implement real-time updates with WebSockets
4. Add file upload capabilities
5. Implement audit logging
6. Add email notifications
