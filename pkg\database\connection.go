package database

import (
	"database/sql"
	"fmt"
	"log"
	"sync"

	"github.com/bridge/bridge/pkg/config"
	"github.com/bridge/bridge/pkg/models"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

type Manager struct {
	config      *config.Config
	masterDB    *sql.DB
	tenantDBs   map[uuid.UUID]*sql.DB
	tenantMutex sync.RWMutex
}

var (
	dbManager *Manager
	once      sync.Once
)

func GetManager() *Manager {
	once.Do(func() {
		cfg := config.Load()
		var err error
		dbManager, err = NewManager(cfg)
		if err != nil {
			log.Fatal("Failed to initialize database manager:", err)
		}
	})
	return dbManager
}

func NewManager(cfg *config.Config) (*Manager, error) {
	manager := &Manager{
		config:    cfg,
		tenantDBs: make(map[uuid.UUID]*sql.DB),
	}

	// Connect to master database
	masterDSN := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Name,
		cfg.Database.SSLMode,
	)

	var err error
	manager.masterDB, err = sql.Open("postgres", masterDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to master database: %w", err)
	}

	if err := manager.masterDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping master database: %w", err)
	}

	log.Println("Connected to master database")
	return manager, nil
}

func (m *Manager) GetMasterDB() *sql.DB {
	return m.masterDB
}

func (m *Manager) GetTenantDB(tenantID uuid.UUID) (*sql.DB, error) {
	m.tenantMutex.RLock()
	if db, exists := m.tenantDBs[tenantID]; exists {
		m.tenantMutex.RUnlock()
		return db, nil
	}
	m.tenantMutex.RUnlock()

	// Need to establish connection
	m.tenantMutex.Lock()
	defer m.tenantMutex.Unlock()

	// Double-check after acquiring write lock
	if db, exists := m.tenantDBs[tenantID]; exists {
		return db, nil
	}

	// Get tenant info from master DB
	tenant, err := m.getTenantByID(tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant info: %w", err)
	}

	if !tenant.IsActive() {
		return nil, fmt.Errorf("tenant is not active")
	}

	// Connect to tenant database
	tenantDSN := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		m.config.Database.Host,
		m.config.Database.Port,
		m.config.Database.User,
		m.config.Database.Password,
		tenant.DatabaseName,
		m.config.Database.SSLMode,
	)

	db, err := sql.Open("postgres", tenantDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to tenant database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping tenant database: %w", err)
	}

	m.tenantDBs[tenantID] = db
	log.Printf("Connected to tenant database: %s", tenant.DatabaseName)
	return db, nil
}

func (m *Manager) getTenantByID(tenantID uuid.UUID) (*models.Tenant, error) {
	query := `
		SELECT id, name, subdomain, database_name, plan, status, 
		       settings, created_at, updated_at
		FROM tenants 
		WHERE id = $1 AND status != 'deleted'
	`

	var tenant models.Tenant
	var settingsJSON []byte

	err := m.masterDB.QueryRow(query, tenantID).Scan(
		&tenant.ID,
		&tenant.Name,
		&tenant.Subdomain,
		&tenant.DatabaseName,
		&tenant.Plan,
		&tenant.Status,
		&settingsJSON,
		&tenant.CreatedAt,
		&tenant.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// Parse settings JSON (simplified for now)
	tenant.Settings = models.DefaultTenantSettings(tenant.Plan)

	return &tenant, nil
}

func (m *Manager) CreateTenantDatabase(tenant *models.Tenant) error {
	// Create database
	createDBQuery := fmt.Sprintf("CREATE DATABASE %s", tenant.DatabaseName)
	_, err := m.masterDB.Exec(createDBQuery)
	if err != nil {
		return fmt.Errorf("failed to create tenant database: %w", err)
	}

	// Connect to the new database and run migrations
	db, err := m.GetTenantDB(tenant.ID)
	if err != nil {
		return fmt.Errorf("failed to connect to new tenant database: %w", err)
	}

	// Run tenant schema migrations
	if err := m.runTenantMigrations(db); err != nil {
		return fmt.Errorf("failed to run tenant migrations: %w", err)
	}

	log.Printf("Created and initialized tenant database: %s", tenant.DatabaseName)
	return nil
}

func (m *Manager) runTenantMigrations(db *sql.DB) error {
	// This is a simplified migration - in production, use a proper migration tool
	migrations := []string{
		`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
		`CREATE TABLE users (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			tenant_id UUID NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			password_hash VARCHAR(255) NOT NULL,
			first_name VARCHAR(100) NOT NULL,
			last_name VARCHAR(100) NOT NULL,
			role VARCHAR(20) NOT NULL DEFAULT 'member',
			is_active BOOLEAN DEFAULT true,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);`,
		// Add more table creation statements here
	}

	for _, migration := range migrations {
		if _, err := db.Exec(migration); err != nil {
			return fmt.Errorf("migration failed: %w", err)
		}
	}

	return nil
}

func (m *Manager) Close() error {
	m.tenantMutex.Lock()
	defer m.tenantMutex.Unlock()

	// Close all tenant connections
	for tenantID, db := range m.tenantDBs {
		if err := db.Close(); err != nil {
			log.Printf("Error closing tenant DB %s: %v", tenantID, err)
		}
	}

	// Close master connection
	if m.masterDB != nil {
		return m.masterDB.Close()
	}

	return nil
}
