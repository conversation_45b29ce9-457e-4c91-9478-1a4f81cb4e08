# Bridge Project Management API Makefile

# Variables
BINARY_NAME=bridge
MAIN_PATH=./cmd/bridge
BUILD_DIR=./build
MIGRATION_DIR=./migrations

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build flags
LDFLAGS=-ldflags "-s -w"

.PHONY: all build clean test deps run dev help setup-db migrate-master migrate-tenant

# Default target
all: clean deps test build

# Build the application
build:
	@echo Building $(BINARY_NAME)...
	@if not exist $(BUILD_DIR) mkdir $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# Build for different platforms
build-linux:
	@echo "Building for Linux..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux $(MAIN_PATH)

build-windows:
	@echo "Building for Windows..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME).exe $(MAIN_PATH)

build-darwin:
	@echo "Building for macOS..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin $(MAIN_PATH)

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Run the application in development mode
dev:
	@echo "Running in development mode..."
	$(GOCMD) run $(MAIN_PATH)

# Run the application
run: build
	@echo "Running $(BINARY_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME)

# Database setup
setup-db:
	@echo "Setting up databases..."
	@echo "Creating master database..."
	createdb bridge_master || echo "Master database already exists"
	@echo "Running master migrations..."
	psql -d bridge_master -f $(MIGRATION_DIR)/001_create_master_schema.sql
	@echo "Setting up sample tenant..."
	psql -d bridge_master -f scripts/setup.sql

# Create tenant database
create-tenant-db:
	@echo "Creating tenant database..."
	createdb bridge_tenant_acme || echo "Tenant database already exists"
	@echo "Running tenant migrations..."
	psql -d bridge_tenant_acme -f $(MIGRATION_DIR)/002_create_tenant_schema.sql
	@echo "Adding sample data..."
	psql -d bridge_tenant_acme -f scripts/sample_data.sql

# Run master database migrations
migrate-master:
	@echo "Running master database migrations..."
	psql -d bridge_master -f $(MIGRATION_DIR)/001_create_master_schema.sql

# Run tenant database migrations
migrate-tenant:
	@echo "Running tenant database migrations..."
	psql -d bridge_tenant_acme -f $(MIGRATION_DIR)/002_create_tenant_schema.sql

# Lint the code
lint:
	@echo "Running linter..."
	golangci-lint run

# Format the code
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

# Vet the code
vet:
	@echo "Vetting code..."
	$(GOCMD) vet ./...

# Security check
security:
	@echo "Running security check..."
	gosec ./...

# Install development tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t bridge-api .

# Docker run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 --env-file .env bridge-api

# Full setup for new development environment
setup: deps install-tools setup-db create-tenant-db
	@echo "Development environment setup complete!"

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build the application"
	@echo "  build-linux    - Build for Linux"
	@echo "  build-windows  - Build for Windows"
	@echo "  build-darwin   - Build for macOS"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  deps           - Download dependencies"
	@echo "  dev            - Run in development mode"
	@echo "  run            - Build and run the application"
	@echo "  setup-db       - Set up master database"
	@echo "  create-tenant-db - Create and set up tenant database"
	@echo "  migrate-master - Run master database migrations"
	@echo "  migrate-tenant - Run tenant database migrations"
	@echo "  lint           - Run linter"
	@echo "  fmt            - Format code"
	@echo "  vet            - Vet code"
	@echo "  security       - Run security check"
	@echo "  install-tools  - Install development tools"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  setup          - Full development environment setup"
	@echo "  help           - Show this help message"
