package models

import (
	"time"

	"github.com/google/uuid"
)

type Tenant struct {
	ID           uuid.UUID    `json:"id" db:"id"`
	Name         string       `json:"name" db:"name"`
	Subdomain    string       `json:"subdomain" db:"subdomain"`
	DatabaseName string       `json:"database_name" db:"database_name"`
	Plan         TenantPlan   `json:"plan" db:"plan"`
	Status       TenantStatus `json:"status" db:"status"`
	Settings     TenantSettings `json:"settings" db:"settings"`
	CreatedAt    time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at" db:"updated_at"`
}

type TenantPlan string

const (
	PlanFree       TenantPlan = "free"
	PlanPro        TenantPlan = "pro"
	PlanEnterprise TenantPlan = "enterprise"
)

type TenantStatus string

const (
	StatusActive    TenantStatus = "active"
	StatusSuspended TenantStatus = "suspended"
	StatusPending   TenantStatus = "pending"
	StatusDeleted   TenantStatus = "deleted"
)

type TenantSettings struct {
	MaxUsers    int  `json:"max_users"`
	MaxProjects int  `json:"max_projects"`
	MaxStorage  int  `json:"max_storage_mb"`
	Features    []string `json:"features"`
}

type CreateTenantRequest struct {
	Name      string     `json:"name" binding:"required"`
	Subdomain string     `json:"subdomain" binding:"required,min=3,max=63"`
	Plan      TenantPlan `json:"plan" binding:"required"`
	AdminUser CreateUserRequest `json:"admin_user" binding:"required"`
}

type UpdateTenantRequest struct {
	Name     *string       `json:"name,omitempty"`
	Plan     *TenantPlan   `json:"plan,omitempty"`
	Status   *TenantStatus `json:"status,omitempty"`
	Settings *TenantSettings `json:"settings,omitempty"`
}

func (t *Tenant) IsActive() bool {
	return t.Status == StatusActive
}

func (t *Tenant) GetMaxUsers() int {
	switch t.Plan {
	case PlanFree:
		return 5
	case PlanPro:
		return 50
	case PlanEnterprise:
		return t.Settings.MaxUsers
	default:
		return 5
	}
}

func (t *Tenant) GetMaxProjects() int {
	switch t.Plan {
	case PlanFree:
		return 3
	case PlanPro:
		return 50
	case PlanEnterprise:
		return t.Settings.MaxProjects
	default:
		return 3
	}
}

func (t *Tenant) HasFeature(feature string) bool {
	for _, f := range t.Settings.Features {
		if f == feature {
			return true
		}
	}
	return false
}

func DefaultTenantSettings(plan TenantPlan) TenantSettings {
	switch plan {
	case PlanFree:
		return TenantSettings{
			MaxUsers:    5,
			MaxProjects: 3,
			MaxStorage:  100, // 100MB
			Features:    []string{"basic_boards", "basic_tasks"},
		}
	case PlanPro:
		return TenantSettings{
			MaxUsers:    50,
			MaxProjects: 50,
			MaxStorage:  5000, // 5GB
			Features:    []string{"basic_boards", "basic_tasks", "advanced_reporting", "integrations"},
		}
	case PlanEnterprise:
		return TenantSettings{
			MaxUsers:    1000,
			MaxProjects: 1000,
			MaxStorage:  50000, // 50GB
			Features:    []string{"basic_boards", "basic_tasks", "advanced_reporting", "integrations", "sso", "audit_logs"},
		}
	default:
		return DefaultTenantSettings(PlanFree)
	}
}
