package auth

import (
	"testing"
	"time"

	"github.com/bridge/bridge/pkg/config"
	"github.com/bridge/bridge/pkg/models"

	"github.com/google/uuid"
)

func TestService_GenerateToken(t *testing.T) {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret",
			ExpiryHours: 24,
		},
	}

	authService := NewService(cfg)

	user := &models.User{
		ID:       uuid.New(),
		TenantID: uuid.New(),
		Email:    "<EMAIL>",
		Role:     models.RoleAdmin,
	}

	token, expiresAt, err := authService.GenerateToken(user)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	if token == "" {
		t.Error("Token should not be empty")
	}

	if expiresAt.Before(time.Now()) {
		t.Error("Token should not be expired")
	}

	// Verify token can be validated
	claims, err := authService.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate token: %v", err)
	}

	if claims.UserID != user.ID {
		t.Errorf("Expected user ID %v, got %v", user.ID, claims.UserID)
	}

	if claims.TenantID != user.TenantID {
		t.Errorf("Expected tenant ID %v, got %v", user.TenantID, claims.TenantID)
	}

	if claims.Email != user.Email {
		t.Errorf("Expected email %v, got %v", user.Email, claims.Email)
	}

	if claims.Role != string(user.Role) {
		t.Errorf("Expected role %v, got %v", user.Role, claims.Role)
	}
}

func TestService_ValidateToken_InvalidToken(t *testing.T) {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret",
			ExpiryHours: 24,
		},
	}

	authService := NewService(cfg)

	_, err := authService.ValidateToken("invalid-token")
	if err == nil {
		t.Error("Expected error for invalid token")
	}
}

func TestService_ExtractTokenFromHeader(t *testing.T) {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret",
			ExpiryHours: 24,
		},
	}

	authService := NewService(cfg)

	tests := []struct {
		name        string
		header      string
		expectToken string
		expectError bool
	}{
		{
			name:        "Valid Bearer token",
			header:      "Bearer abc123",
			expectToken: "abc123",
			expectError: false,
		},
		{
			name:        "Invalid format - no Bearer",
			header:      "abc123",
			expectToken: "",
			expectError: true,
		},
		{
			name:        "Invalid format - empty",
			header:      "",
			expectToken: "",
			expectError: true,
		},
		{
			name:        "Invalid format - only Bearer",
			header:      "Bearer",
			expectToken: "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := authService.ExtractTokenFromHeader(tt.header)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if token != tt.expectToken {
				t.Errorf("Expected token %v, got %v", tt.expectToken, token)
			}
		})
	}
}
