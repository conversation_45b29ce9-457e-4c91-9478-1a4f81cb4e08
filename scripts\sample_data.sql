-- Sample data for tenant database
-- Run this on a tenant database (e.g., bridge_tenant_acme) after running the tenant schema migration

-- Create a sample admin user
-- Password: "admin123" (hashed with bcrypt)
INSERT INTO users (
    id,
    tenant_id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-446*********', -- This should match the tenant ID from setup.sql
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: "admin123"
    'Admin',
    'User',
    'admin',
    true
) ON CONFLICT (email) DO NOTHING;

-- Create a sample manager user
-- Password: "manager123" (hashed with bcrypt)
INSERT INTO users (
    id,
    tenant_id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-446*********',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: "manager123"
    'Project',
    'Manager',
    'manager',
    true
) ON CONFLICT (email) DO NOTHING;

-- Create a sample member user
-- Password: "member123" (hashed with bcrypt)
INSERT INTO users (
    id,
    tenant_id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-446*********',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: "member123"
    'Team',
    'Member',
    'member',
    true
) ON CONFLICT (email) DO NOTHING;

-- Create a sample project
INSERT INTO projects (
    id,
    tenant_id,
    name,
    description,
    status,
    owner_id,
    start_date,
    end_date
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-446*********',
    'Website Redesign',
    'Complete redesign of the company website with modern UI/UX',
    'active',
    '550e8400-e29b-41d4-a716-************', -- admin user
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '3 months'
) ON CONFLICT (id) DO NOTHING;

-- Create a sample board
INSERT INTO boards (
    id,
    project_id,
    name,
    description,
    position
) VALUES (
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-************',
    'Main Board',
    'Primary project board for tracking all tasks',
    0
) ON CONFLICT (id) DO NOTHING;

-- Create sample columns
INSERT INTO columns (id, board_id, name, position, color) VALUES
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020', 'To Do', 0, '#EF4444'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020', 'In Progress', 1, '#F59E0B'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020', 'Review', 2, '#3B82F6'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020', 'Done', 3, '#10B981')
ON CONFLICT (id) DO NOTHING;

-- Create sample tasks
INSERT INTO tasks (id, column_id, title, description, priority, status, assignee_id, position) VALUES
    ('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-************', 'Design Homepage Mockup', 'Create initial design mockups for the new homepage', 'high', 'todo', '550e8400-e29b-41d4-a716-************', 0),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Set up Development Environment', 'Configure local development environment for the project', 'medium', 'in_progress', '550e8400-e29b-41d4-a716-************', 0),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Review Brand Guidelines', 'Review and approve the updated brand guidelines', 'low', 'review', '550e8400-e29b-41d4-a716-************', 0),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Project Kickoff Meeting', 'Conduct initial project kickoff meeting with stakeholders', 'high', 'done', '550e8400-e29b-41d4-a716-************', 0)
ON CONFLICT (id) DO NOTHING;

-- Add project members
INSERT INTO project_members (project_id, user_id, role) VALUES
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'owner'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'manager'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'member')
ON CONFLICT (project_id, user_id) DO NOTHING;

-- Display created data
SELECT 'Users created:' as info;
SELECT email, first_name, last_name, role FROM users ORDER BY role;

SELECT 'Projects created:' as info;
SELECT name, description, status, start_date, end_date FROM projects;

SELECT 'Boards and columns created:' as info;
SELECT b.name as board_name, c.name as column_name, c.position, c.color 
FROM boards b 
JOIN columns c ON b.id = c.board_id 
ORDER BY b.name, c.position;

SELECT 'Tasks created:' as info;
SELECT t.title, c.name as column, t.priority, t.status, u.first_name || ' ' || u.last_name as assignee
FROM tasks t
JOIN columns c ON t.column_id = c.id
LEFT JOIN users u ON t.assignee_id = u.id
ORDER BY c.position, t.position;
