package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/bridge/bridge/internal/server"
	"github.com/bridge/bridge/pkg/config"
	"github.com/bridge/bridge/pkg/database"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database manager
	dbManager := database.GetManager()
	defer func() {
		if err := dbManager.Close(); err != nil {
			log.Printf("Error closing database connections: %v", err)
		}
	}()

	// Create and start server
	srv := server.New(cfg)

	// Start server
	log.Printf("Starting server on port %s", cfg.Server.Port)
	log.Printf("Server mode: %s", cfg.Server.GinMode)

	// Setup graceful shutdown
	go func() {
		if err := srv.Start(); err != nil {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")
	srv.Shutdown()
}
