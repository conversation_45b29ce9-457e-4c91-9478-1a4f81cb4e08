# Bridge - Multi-Tenant Project Management API

A Go-based REST API for a multi-tenant project management application with physically separated tenant data.

## Features

- **Multi-tenancy**: Physical database separation per tenant for complete data isolation
- **Authentication**: JWT-based authentication with role-based access control
- **Project Management**: Projects, boards, columns, and tasks management
- **User Management**: User roles (admin, manager, member, viewer) with appropriate permissions
- **RESTful API**: Clean REST endpoints with proper HTTP status codes
- **Database Migrations**: Automated database schema management
- **CORS Support**: Configurable CORS for frontend integration

## Architecture

### Multi-Tenant Strategy
- **Master Database**: Stores tenant metadata and configuration
- **Tenant Databases**: Separate database per tenant for complete data isolation
- **Dynamic Connection Management**: Automatic tenant database connection handling

### Security
- JWT tokens with tenant identification
- Role-based access control (RBAC)
- Password hashing with bcrypt
- Tenant isolation at database level

## Quick Start

### Prerequisites
- Go 1.21 or higher
- PostgreSQL 12 or higher

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bridge
```

2. Install dependencies:
```bash
go mod download
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up PostgreSQL:
```bash
# Using Make (recommended)
make setup-db
make create-tenant-db

# Or manually:
# Create master database
createdb bridge_master

# Run master database migrations
psql -d bridge_master -f migrations/001_create_master_schema.sql
```

5. Run the application:
```bash
# Using Make (recommended)
make dev

# Or directly with Go
go run cmd/bridge/main.go
```

The API will be available at `http://localhost:8080`

## Configuration

Environment variables (see `.env.example`):

- `PORT`: Server port (default: 8080)
- `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`: Database connection
- `JWT_SECRET`: JWT signing secret
- `CORS_ALLOWED_ORIGINS`: Allowed CORS origins

## API Endpoints

### Health Check
- `GET /health` - Service health status

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token

### User Profile
- `GET /api/v1/profile` - Get current user profile
- `PUT /api/v1/profile` - Update current user profile

### User Management (Admin/Manager only)
- `POST /api/v1/users` - Create new user
- `GET /api/v1/users` - List users
- `GET /api/v1/users/:id` - Get user by ID
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Soft delete user

### Project Management (Coming Soon)
- `GET /api/v1/projects` - List projects
- `POST /api/v1/projects` - Create project
- `GET /api/v1/projects/:id` - Get project
- `PUT /api/v1/projects/:id` - Update project

### Board Management (Coming Soon)
- `GET /api/v1/boards` - List boards
- `POST /api/v1/boards` - Create board

### Task Management (Coming Soon)
- `GET /api/v1/tasks` - List tasks
- `POST /api/v1/tasks` - Create task

## Database Schema

### Master Database
- `tenants` - Tenant information and configuration

### Tenant Databases
- `users` - Tenant users
- `projects` - Projects
- `boards` - Project boards
- `columns` - Board columns
- `tasks` - Tasks
- `project_members` - Project membership

## User Roles

- **Admin**: Full access to all features and user management
- **Manager**: Can manage projects and users (limited)
- **Member**: Can create and edit tasks, view projects
- **Viewer**: Read-only access

## Tenant Identification

The API supports multiple methods for tenant identification:

1. **JWT Token**: Tenant ID embedded in authentication token
2. **X-Tenant-ID Header**: Explicit tenant ID in request header
3. **Subdomain**: Extract tenant from subdomain (e.g., `acme.yourdomain.com`)

## Development

### Project Structure
```
bridge/
├── cmd/
│   └── bridge/         # Main application entry point
├── pkg/                # Public library code
│   ├── api/           # API layer (handlers, routes)
│   ├── auth/          # Authentication utilities
│   ├── config/        # Configuration management
│   ├── database/      # Database utilities
│   └── models/        # Data models
├── internal/          # Private application code
│   ├── server/        # HTTP server setup
│   ├── services/      # Business logic services
│   └── middleware/    # HTTP middleware
├── migrations/        # Database migrations
├── scripts/           # Build and setup scripts
├── Makefile          # Build automation
└── README.md
```

### Adding New Features

1. Define models in `pkg/models/`
2. Create services in `internal/services/`
3. Implement handlers in `pkg/api/handlers/`
4. Add routes in `pkg/api/routes/router.go`
5. Update database migrations in `migrations/`

### Testing

```bash
# Run tests
make test

# Run with coverage
make test-coverage

# Or directly with Go
go test ./...
go test -cover ./...
```

## Deployment

### Docker (Coming Soon)
```bash
docker build -t bridge-api .
docker run -p 8080:8080 bridge-api
```

### Environment Variables for Production
- Set strong `JWT_SECRET`
- Configure proper database credentials
- Set `GIN_MODE=release`
- Configure appropriate CORS origins

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Add your license here]
