package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/bridge/bridge/pkg/api/routes"
	"github.com/bridge/bridge/pkg/config"

	"github.com/gin-gonic/gin"
)

type Server struct {
	config     *config.Config
	httpServer *http.Server
	router     *gin.Engine
}

func New(cfg *config.Config) *Server {
	router := routes.SetupRouter(cfg)

	return &Server{
		config: cfg,
		router: router,
		httpServer: &http.Server{
			Addr:         ":" + cfg.Server.Port,
			Handler:      router,
			ReadTimeout:  15 * time.Second,
			WriteTimeout: 15 * time.Second,
			IdleTimeout:  60 * time.Second,
		},
	}
}

func (s *Server) Start() error {
	log.Printf("Starting HTTP server on port %s", s.config.Server.Port)
	return s.httpServer.ListenAndServe()
}

func (s *Server) Shutdown() error {
	log.Println("Shutting down HTTP server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := s.httpServer.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	log.Println("HTTP server stopped")
	return nil
}

func (s *Server) Router() *gin.Engine {
	return s.router
}
