package routes

import (
	"github.com/bridge/bridge/internal/middleware"
	"github.com/bridge/bridge/pkg/api/handlers"
	"github.com/bridge/bridge/pkg/auth"
	"github.com/bridge/bridge/pkg/config"

	"github.com/gin-gonic/gin"
)

func SetupRouter(cfg *config.Config) *gin.Engine {
	// Set Gin mode
	gin.SetMode(cfg.Server.GinMode)

	router := gin.New()

	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware(cfg))

	// Initialize services
	authService := auth.NewService(cfg)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)
	userHandler := handlers.NewUserHandler()

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{
			"status": "ok",
			"service": "bridge-api",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")

	// Public routes (no authentication required)
	public := v1.Group("/")
	{
		// Tenant identification can be done via subdomain or header
		public.Use(middleware.TenantFromSubdomain())
		public.Use(middleware.TenantFromHeader())

		// Authentication endpoints
		auth := public.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
		}
	}

	// Protected routes (authentication required)
	protected := v1.Group("/")
	{
		protected.Use(middleware.AuthMiddleware(authService))
		protected.Use(middleware.TenantMiddleware())

		// User profile endpoints
		profile := protected.Group("/profile")
		{
			profile.GET("", authHandler.GetProfile)
			profile.PUT("", authHandler.UpdateProfile)
		}

		// User management endpoints (admin/manager only)
		users := protected.Group("/users")
		{
			users.Use(middleware.RequireManagerOrAdmin())
			users.POST("", userHandler.CreateUser)
			users.GET("", userHandler.ListUsers)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}

		// Project management endpoints
		projects := protected.Group("/projects")
		{
			// TODO: Implement project handlers
			projects.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Projects endpoint - coming soon"})
			})
		}

		// Board management endpoints
		boards := protected.Group("/boards")
		{
			// TODO: Implement board handlers
			boards.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Boards endpoint - coming soon"})
			})
		}

		// Task management endpoints
		tasks := protected.Group("/tasks")
		{
			// TODO: Implement task handlers
			tasks.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Tasks endpoint - coming soon"})
			})
		}
	}

	// Admin routes (admin only)
	admin := v1.Group("/admin")
	{
		admin.Use(middleware.AuthMiddleware(authService))
		admin.Use(middleware.RequireAdmin())

		// Tenant management (super admin functionality)
		tenants := admin.Group("/tenants")
		{
			// TODO: Implement tenant management handlers
			tenants.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Tenant management - coming soon"})
			})
		}
	}

	return router
}
