package models

import (
	"time"

	"github.com/google/uuid"
)

type Project struct {
	ID          uuid.UUID     `json:"id" db:"id"`
	TenantID    uuid.UUID     `json:"tenant_id" db:"tenant_id"`
	Name        string        `json:"name" db:"name"`
	Description string        `json:"description" db:"description"`
	Status      ProjectStatus `json:"status" db:"status"`
	OwnerID     uuid.UUID     `json:"owner_id" db:"owner_id"`
	Owner       *User         `json:"owner,omitempty"`
	StartDate   *time.Time    `json:"start_date" db:"start_date"`
	EndDate     *time.Time    `json:"end_date" db:"end_date"`
	CreatedAt   time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at" db:"updated_at"`
}

type ProjectStatus string

const (
	ProjectStatusPlanning   ProjectStatus = "planning"
	ProjectStatusActive     ProjectStatus = "active"
	ProjectStatusOnHold     ProjectStatus = "on_hold"
	ProjectStatusCompleted  ProjectStatus = "completed"
	ProjectStatusCancelled  ProjectStatus = "cancelled"
)

type Board struct {
	ID          uuid.UUID   `json:"id" db:"id"`
	ProjectID   uuid.UUID   `json:"project_id" db:"project_id"`
	Name        string      `json:"name" db:"name"`
	Description string      `json:"description" db:"description"`
	Position    int         `json:"position" db:"position"`
	CreatedAt   time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at" db:"updated_at"`
	Columns     []Column    `json:"columns,omitempty"`
}

type Column struct {
	ID        uuid.UUID `json:"id" db:"id"`
	BoardID   uuid.UUID `json:"board_id" db:"board_id"`
	Name      string    `json:"name" db:"name"`
	Position  int       `json:"position" db:"position"`
	Color     string    `json:"color" db:"color"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	Tasks     []Task    `json:"tasks,omitempty"`
}

type Task struct {
	ID          uuid.UUID    `json:"id" db:"id"`
	ColumnID    uuid.UUID    `json:"column_id" db:"column_id"`
	Title       string       `json:"title" db:"title"`
	Description string       `json:"description" db:"description"`
	Priority    TaskPriority `json:"priority" db:"priority"`
	Status      TaskStatus   `json:"status" db:"status"`
	AssigneeID  *uuid.UUID   `json:"assignee_id" db:"assignee_id"`
	Assignee    *User        `json:"assignee,omitempty"`
	Position    int          `json:"position" db:"position"`
	DueDate     *time.Time   `json:"due_date" db:"due_date"`
	CreatedAt   time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at" db:"updated_at"`
}

type TaskPriority string

const (
	PriorityLow      TaskPriority = "low"
	PriorityMedium   TaskPriority = "medium"
	PriorityHigh     TaskPriority = "high"
	PriorityCritical TaskPriority = "critical"
)

type TaskStatus string

const (
	TaskStatusTodo       TaskStatus = "todo"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusReview     TaskStatus = "review"
	TaskStatusDone       TaskStatus = "done"
)

// Request/Response DTOs
type CreateProjectRequest struct {
	Name        string     `json:"name" binding:"required"`
	Description string     `json:"description"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
}

type UpdateProjectRequest struct {
	Name        *string        `json:"name,omitempty"`
	Description *string        `json:"description,omitempty"`
	Status      *ProjectStatus `json:"status,omitempty"`
	StartDate   *time.Time     `json:"start_date,omitempty"`
	EndDate     *time.Time     `json:"end_date,omitempty"`
}

type CreateBoardRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

type CreateColumnRequest struct {
	Name  string `json:"name" binding:"required"`
	Color string `json:"color"`
}

type CreateTaskRequest struct {
	Title       string        `json:"title" binding:"required"`
	Description string        `json:"description"`
	Priority    TaskPriority  `json:"priority"`
	AssigneeID  *uuid.UUID    `json:"assignee_id"`
	DueDate     *time.Time    `json:"due_date"`
}

type UpdateTaskRequest struct {
	Title       *string       `json:"title,omitempty"`
	Description *string       `json:"description,omitempty"`
	Priority    *TaskPriority `json:"priority,omitempty"`
	Status      *TaskStatus   `json:"status,omitempty"`
	AssigneeID  *uuid.UUID    `json:"assignee_id,omitempty"`
	Position    *int          `json:"position,omitempty"`
	ColumnID    *uuid.UUID    `json:"column_id,omitempty"`
	DueDate     *time.Time    `json:"due_date,omitempty"`
}
