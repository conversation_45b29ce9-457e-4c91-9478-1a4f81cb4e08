-- Setup script for Bridge Project Management API
-- Run this script to set up the master database and create a sample tenant

-- First, make sure you've created the master database:
-- createdb bridge_master

-- Then run the master schema migration:
-- \i migrations/001_create_master_schema.sql

-- Create a sample tenant
INSERT INTO tenants (
    id,
    name,
    subdomain,
    database_name,
    plan,
    status,
    settings
) VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'Acme Corporation',
    'acme',
    'bridge_tenant_acme',
    'pro',
    'active',
    '{
        "max_users": 50,
        "max_projects": 50,
        "max_storage": 5000,
        "features": ["basic_boards", "basic_tasks", "advanced_reporting", "integrations"]
    }'::jsonb
) ON CONFLICT (subdomain) DO NOTHING;

-- Display the created tenant
SELECT 
    id,
    name,
    subdomain,
    database_name,
    plan,
    status,
    created_at
FROM tenants 
WHERE subdomain = 'acme';

-- Instructions for next steps:
-- 1. Create the tenant database: createdb bridge_tenant_acme
-- 2. Run tenant migrations: psql -d bridge_tenant_acme -f migrations/002_create_tenant_schema.sql
-- 3. Create a sample admin user in the tenant database (see sample_data.sql)
