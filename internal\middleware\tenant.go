package middleware

import (
	"net/http"
	"strings"

	"github.com/bridge/bridge/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get tenant ID from JWT claims (set by AuthMiddleware)
		tenantIDInterface, exists := c.Get("tenant_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Tenant ID not found in token",
			})
			c.Abort()
			return
		}

		tenantID, ok := tenantIDInterface.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid tenant ID format",
			})
			c.Abort()
			return
		}

		// Get tenant database connection
		dbManager := database.GetManager()
		tenantDB, err := dbManager.GetTenantDB(tenantID)
		if err != nil {
			c.<PERSON>(http.StatusForbidden, gin.H{
				"error": "Unable to access tenant data",
			})
			c.Abort()
			return
		}

		// Set tenant database in context
		c.Set("tenant_db", tenantDB)
		c.Set("tenant_id", tenantID)

		c.Next()
	}
}

func TenantFromHeader() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Alternative method: Get tenant from X-Tenant-ID header
		// This can be used for public endpoints or tenant identification
		tenantHeader := c.GetHeader("X-Tenant-ID")
		if tenantHeader != "" {
			tenantID, err := uuid.Parse(tenantHeader)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Invalid tenant ID format in header",
				})
				c.Abort()
				return
			}

			// Validate tenant exists and is active
			dbManager := database.GetManager()
			tenantDB, err := dbManager.GetTenantDB(tenantID)
			if err != nil {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Invalid or inactive tenant",
				})
				c.Abort()
				return
			}

			c.Set("tenant_db", tenantDB)
			c.Set("tenant_id", tenantID)
		}

		c.Next()
	}
}

func TenantFromSubdomain() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract subdomain from Host header
		host := c.GetHeader("Host")
		if host == "" {
			c.Next()
			return
		}

		// Simple subdomain extraction (you might want to make this more robust)
		parts := strings.Split(host, ".")
		if len(parts) < 3 {
			c.Next()
			return
		}

		subdomain := parts[0]
		if subdomain == "www" || subdomain == "api" {
			c.Next()
			return
		}

		// Look up tenant by subdomain
		dbManager := database.GetManager()
		masterDB := dbManager.GetMasterDB()

		var tenantID uuid.UUID
		query := "SELECT id FROM tenants WHERE subdomain = $1 AND status = 'active'"
		err := masterDB.QueryRow(query, subdomain).Scan(&tenantID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Tenant not found",
			})
			c.Abort()
			return
		}

		// Get tenant database
		tenantDB, err := dbManager.GetTenantDB(tenantID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Unable to access tenant data",
			})
			c.Abort()
			return
		}

		c.Set("tenant_db", tenantDB)
		c.Set("tenant_id", tenantID)
		c.Set("subdomain", subdomain)

		c.Next()
	}
}
